import type { ApiResponse } from '@/shared/types/api-response';
import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ZodType } from 'zod';
import { Env } from '@/core/config/Env';
import { useAuthStore } from '@/features/auth/store/auth.store';
import { removeToken } from '@/features/auth/utils/token';
import { refreshTokenManager } from '@/features/auth/utils/refreshTokenManager';
import { parseZodError } from '@/shared/utils/utils';
import axios, { AxiosHeaders } from 'axios';
import Cookies from 'js-cookie';
import { COOKIE_SESSION_KEY } from '../../features/auth/constant';
import { X_TENANT_ID } from '../../shared/constants/global';
import { HttpMethod } from './http.enum';

const baseURL = Env.NEXT_PUBLIC_API_SERVER;

const isBrowser = () => typeof window !== 'undefined';

const axiosInstance: AxiosInstance = axios.create({
  baseURL,
  withCredentials: true,
  headers: new AxiosHeaders({ 'Content-Type': 'application/json' }),
  timeout: 50000,
});

axiosInstance.interceptors.request.use(async (config) => {
  const headers = new AxiosHeaders(config.headers || {});
  headers.set('x-tenant-id', X_TENANT_ID);

  // Skip token refresh for the refresh endpoint itself to avoid infinite loops
  const isRefreshEndpoint = config.url?.includes('/auth/refresh');

  if (isBrowser() && !isRefreshEndpoint) {
    // Ensure token is valid before making the request
    try {
      await refreshTokenManager.ensureValidToken();
    } catch (error) {
      console.error('Failed to ensure valid token:', error);
      // Continue with the request even if refresh fails
      // The response interceptor will handle 401 errors
    }

    // Get token from cookies (might be updated after refresh)
    const token = Cookies.get(COOKIE_SESSION_KEY);
    if (token && !config.headers?.Authorization) {
      headers.set('Authorization', `Bearer ${token}`);
    }
  }

  config.headers = headers;
  return config;
});

axiosInstance.interceptors.response.use(
  response => response,
  (error) => {
    // Check for 401 or 403 status codes
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.log('Authentication error detected, logging out user');

      // Clear the token from cookies
      if (typeof isBrowser()) {
        removeToken(COOKIE_SESSION_KEY);

        // Reset auth store if we're in the browser
        const resetAuthStore = useAuthStore.getState().reset;
        resetAuthStore();

        // Redirect to login page
        // Use a small delay to ensure store is reset first
        // setTimeout(() => {
        //   redirect('/signin');
        // }, 100);
      }
    }

    if (error.isAxiosError) {
      const err = error as AxiosError;
      const apiResponseError = err.response?.data as ApiResponse;
      console.log('apiResponseError', apiResponseError);
      // if (apiResponseError) {
      //   toast.error(apiResponseError.message);
      // } else {
      //   toast.error(err.message);
      // }
    }

    return Promise.reject(error);
  },
);

const normalizeAxiosError = (error: any) => {
  if (axios.isAxiosError(error)) {
    const responseData = error.response?.data ?? {};

    // Check if the response follows our API error format
    if (responseData.statusCode && responseData.message) {
      return responseData as ApiResponse<null>;
    }

    // Fallback for other error formats
    const detail
      = responseData?.errors?.[0]?.message
        ?? responseData?.error
        ?? responseData?.message
        ?? error.message;

    // Create a standardized error response
    return {
      statusCode: error.response?.status || 500,
      message: detail || 'An unexpected error occurred',
      data: null,
      errorDetails: {
        error: responseData?.error || 'Unknown error',
        statusCode: error.response?.status || 500,
        timestamp: new Date().toISOString(),
        path: error.config?.url || '',
      },
    } as ApiResponse<null>;
  }

  // For non-Axios errors
  return {
    statusCode: 500,
    message: error.message || 'An unexpected error occurred',
    data: null,
    errorDetails: {
      error: 'Unknown error',
      statusCode: 500,
      timestamp: new Date().toISOString(),
      path: '',
    },
  } as ApiResponse<null>;
};

async function request<T, TZod>(
  params: {
    method: HttpMethod;
    url: string;
    options?: AxiosRequestConfig & { data?: unknown };
    schemaValidation?: ZodType<TZod>;
  },
): Promise<ApiResponse<T>> {
  const { method, url, options = {}, schemaValidation } = params;

  if (schemaValidation && options.data !== undefined) {
    const result = schemaValidation.safeParse(options.data);
    if (!result.success) {
      // Create a standardized validation error response
      const errorResponse: ApiResponse<null> = {
        statusCode: 400,
        message: 'Validation error',
        data: null,
        errorDetails: {
          error: parseZodError(result.error),
          statusCode: 400,
          timestamp: new Date().toISOString(),
          path: url,
        },
      };
      throw errorResponse;
    }
  }
  try {
    const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.request<ApiResponse<T>>({
      method,
      url,
      ...options,
    });

    // Return the standardized response
    return response.data;
  } catch (err) {
    // Normalize and throw the error in our standard format
    throw normalizeAxiosError(err);
  }
}

export const http = {
  get: <T, TZod = any>(
    params: { url: string; options?: AxiosRequestConfig; schemaValidation?: ZodType<TZod> },
  ): Promise<ApiResponse<T>> => request<T, TZod>(
    { method: HttpMethod.GET, url: params.url, options: params.options, schemaValidation: params.schemaValidation },
  ),

  post: <T, TZod = any>(
    params: { url: string; data?: unknown; options?: AxiosRequestConfig; schemaValidation?: ZodType<TZod> },
  ): Promise<ApiResponse<T>> => request<T, TZod>({ method: HttpMethod.POST, url: params.url, options: { ...params.options, data: params.data }, schemaValidation: params.schemaValidation }),

  put: <T, TZod = any>(
    params: { url: string; data?: unknown; options?: AxiosRequestConfig; schemaValidation?: ZodType<TZod> },
  ): Promise<ApiResponse<T>> => request<T, TZod>({ method: HttpMethod.PUT, url: params.url, options: { ...params.options, data: params.data }, schemaValidation: params.schemaValidation }),

  patch: <T, TZod = any>(
    params: { url: string; data?: unknown; options?: AxiosRequestConfig; schemaValidation?: ZodType<TZod> },
  ): Promise<ApiResponse<T>> => request<T, TZod>({ method: HttpMethod.PATCH, url: params.url, options: { ...params.options, data: params.data }, schemaValidation: params.schemaValidation }),

  delete: <T, TZod = any>(
    params: { url: string; options?: AxiosRequestConfig; schemaValidation?: ZodType<TZod> },
  ): Promise<ApiResponse<T>> => request<T, TZod>({ method: HttpMethod.DELETE, url: params.url, options: params.options, schemaValidation: params.schemaValidation }),
};

export const httpService = { ...http };
