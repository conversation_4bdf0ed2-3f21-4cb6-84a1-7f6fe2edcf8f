{"RootLayout": {"home_link": "Home", "about_link": "About", "counter_link": "Counter", "portfolio_link": "Portfolio", "sign_in_link": "Sign in", "sign_up_link": "Sign up"}, "BaseTemplate": {"description": "Starter code for your Nextjs Boilerplate with Tailwind CSS", "made_with": "Made with <author></author>."}, "Index": {"meta_title": "Next.js Boilerplate Presentation", "meta_description": "Next js Boilerplate is the perfect starter code for your project. Build your React application with the Next.js framework.", "sponsors_title": "Sponsors"}, "Counter": {"meta_title": "Counter", "meta_description": "An example of DB operation", "loading_counter": "Loading counter...", "security_powered_by": "Security, bot detection and rate limiting powered by"}, "CounterForm": {"presentation": "The counter is stored in the database and incremented by the value you provide.", "label_increment": "Increment by", "button_increment": "Increment"}, "CurrentCount": {"count": "Count: {count}"}, "About": {"meta_title": "About", "meta_description": "About page description", "about_paragraph": "Welcome to our About page! We are a team of passionate individuals dedicated to creating amazing software.", "translation_powered_by": "Translation powered by"}, "Portfolio": {"meta_title": "Portfolio", "meta_description": "Welcome to my portfolio page!", "presentation": "Welcome to my portfolio page! Here you will find a carefully curated collection of my work and accomplishments. Through this portfolio, I'm to showcase my expertise, creativity, and the value I can bring to your projects.", "portfolio_name": "Portfolio {name}", "error_reporting_powered_by": "Error reporting powered by", "coverage_powered_by": "Code coverage powered by"}, "PortfolioSlug": {"meta_title": "Portfolio {slug}", "meta_description": "Portfolio {slug} description", "header": "Portfolio {slug}", "content": "Created a set of promotional materials and branding elements for a corporate event. Crafted a visually unified theme, encompassing a logo, posters, banners, and digital assets. Integrated the client's brand identity while infusing it with a contemporary and innovative approach. Garnered favorable responses from event attendees, resulting in a successful event with heightened participant engagement and increased brand visibility.", "code_review_powered_by": "Code review powered by"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "Dashboard": {"meta_title": "Dashboard", "hello_message": "Hello {email}!", "alternative_message": "Want to build your SaaS faster using the same stack? Try <url></url>."}, "UserProfile": {"meta_title": "User Profile"}, "DashboardLayout": {"dashboard_link": "Dashboard", "user_profile_link": "Manage your account", "sign_out": "Sign out"}, "TaskWorkflow": {"mvv_home": "MVV Home", "chat_ai": "Chat with AI"}, "Project": {"create_new_project_button": "New Project", "form_title": "Create New Project", "form_title_detail": "Project Detail", "form_subtitle": "Fill in the details to create a new project", "client_section": "Client Information", "client_name": "Client Name", "address": "Address", "tax_code": "Tax Code", "contact_person": "Contact Person", "tel": "Telephone", "email": "Email", "industry": "Industry", "project_section": "Project Information", "project_name": "Project Name", "project_type": "Project Type", "campaign": "Campaign", "company": "Company", "type": "Type", "description": "Description", "status": "Status", "start_date": "Start Date", "end_date": "End Date", "project_owner": "Project Owner", "team_members": "Team Members", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Project created successfully!", "error_message": "Failed to create project. Please try again.", "cancel_button": "Cancel", "back_button": "Back", "create_button": "Create Project", "edit_button": "Edit Project", "creating_button": "Creating...", "update_button": "Update Project", "updating_button": "Updating...", "placeholder": {"client_name": "Enter client name", "address": "Enter address", "tax_code": "Enter tax code", "contact_person": "Enter contact person name", "tel": "Enter telephone number", "email": "Enter email address", "industry": "Enter industry", "project_name": "Enter project name", "project_type": "Select project type", "company": "Select company", "campaign": "Select campaign", "description": "Enter project description", "status": "Select status", "start_date": "Pick a date", "end_date": "Pick a date", "project_owner": "Select project owner", "select_team_members": "Select team members", "search_team_members": "Search team members...", "select_report_input": "Select multiple", "search_research": "Search research..."}, "project_type_options": {"branding": "Branding", "general_consulting": "General Consulting", "diagnostics": "Diagnostics"}, "campaign_options": {"corporate": "Corporate", "crisis_management": "Crisis Management", "event": "Event", "gr_advocacy": "GR-Advocacy", "imc": "IMC", "market_research": "Market Research", "media_relation_pr": "Media Relation - PR", "mi_brand_branding": "Mibrand Branding", "product_launch": "Product Launch", "social_digital_corporate": "Social & Digital Corporate", "social_digital_product": "Social Media & Digital Product", "tvc_video_production": "TVC/Video Production"}, "status_options": {"planned": "Planned", "in_progress": "In Progress", "completed": "Completed", "on_hold": "On Hold"}, "delete_project": "Delete project", "confirm_delete_project": "Are you sure you want to delete this project? This action cannot be undone.", "confirm_delete_project_title": "Delete Project", "project_deleted_successfully": "Project deleted successfully", "error_deleting_project": "Error deleting project. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_projects": "Search projects...", "filter_projects": "Filter Projects", "all_statuses": "All Statuses", "all_types": "All Types", "all_campaigns": "All Campaigns", "clear_all_filters": "Clear All Filters", "remove_status_filter": "Remove status filter", "remove_type_filter": "Remove type filter", "remove_campaign_filter": "Remove campaign filter", "remove_start_date_filter": "Remove start date filter", "remove_end_date_filter": "Remove end date filter", "remove_search_filter": "Remove search filter", "status_filter": "Status: {status}", "type_filter": "Type: {type}", "campaign_filter": "Campaign: {campaign}", "start_date_filter": "Start Date: {date}", "end_date_filter": "End Date: {date}", "search_filter": "Search: {query}", "search": "Search", "edit": "Edit", "create": "Create", "update": "Update", "creating": "Creating...", "updating": "Updating...", "research_name": "Research name", "research_framework": "Research framework", "research_template": "Research template", "create_research": "Create Research", "create_report": "Create Report", "edit_research": "Edit Research", "edit_report": "Edit Report", "update_research": "Update Research", "delete_research": "Delete Research", "research_form_description": "Please enter all the required information to proceed.", "search_research_placeholder": "Search research analysis", "search_report_placeholder": "Search report", "confirm_delete_research": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "select_framework": "Select framework", "select_template": "Select template", "search_frameworks": "Search frameworks...", "search_templates": "Search templates...", "no_research_items": "No research items found. Create your first research item to get started.", "report_input": "Report input", "report_type": "Report type", "select_report_type": "Select", "report_name": "Report name", "research_type": "Research type"}, "TeamMember": {"team_members": "Team Members", "create_new_user_button": "New Member", "form_title": "Add New Team Member", "form_subtitle": "Fill in the details to add a new team member", "user_information": "User Information", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "password": "Password", "roles": "Roles", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Team member created successfully!", "error_message": "Failed to create team member. Please try again.", "cancel_button": "Cancel", "create_button": "Create Team Member", "creating_button": "Creating...", "placeholder": {"first_name": "Enter first name", "last_name": "Enter last name", "email": "Enter email address", "password": "Enter password", "roles": "Select roles"}, "role_options": {"user": "User", "admin": "Admin", "manager": "Manager"}, "delete_user": "Delete team member", "confirm_delete_user": "Are you sure you want to delete <name></name>? This action cannot be undone.", "confirm_delete_user_title": "Delete Team Member", "user_deleted_successfully": "Team member deleted successfully", "error_deleting_user": "Error deleting team member. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_team_members": "Search team members...", "no_team_members_found": "No team members found.", "loading_more_team_members": "Loading more team members...", "try_again": "Try Again", "table": {"no": "No.", "full_name": "Full Name", "email": "Email", "updated_at": "Updated At", "actions": "Actions"}}, "Prompt": {"prompts": "Prompts", "create_new_prompt_button": "New Prompt", "form_title": "Add New Prompt", "form_subtitle": "Fill in the details to add a new prompt", "name": "Name", "description": "Description", "content": "Content", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Prompt created successfully!", "error_message": "Failed to create prompt. Please try again.", "cancel_button": "Cancel", "create_button": "Create Prompt", "creating_button": "Creating...", "placeholder": {"name": "Enter prompt name", "description": "Enter prompt description", "content": "Enter prompt content"}, "delete_prompt": "Delete prompt", "confirm_delete_prompt": "Are you sure you want to delete <name></name>? This action cannot be undone.", "confirm_delete_prompt_title": "Delete Prompt", "prompt_deleted_successfully": "Prompt deleted successfully", "error_deleting_prompt": "Error deleting prompt. Please try again.", "update_form_title": "Update Prompt", "update_form_subtitle": "Update the details of this prompt.", "prompt_information": "Prompt Information", "update_button": "Update", "updating_button": "Updating...", "update_prompt": "Update Prompt", "prompt_updated_successfully": "Prompt updated successfully", "error_updating_prompt": "Error updating prompt. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_prompts": "Search prompts...", "no_prompts_found": "No prompts found.", "loading_more_prompts": "Loading more prompts...", "try_again": "Try Again", "table": {"no": "No.", "name": "Name", "description": "Description", "content": "Content", "updated_at": "Updated At", "actions": "Actions"}}, "Step": {"steps_title": "Steps", "steps_description": "Manage workflow steps and their associated prompts", "order": "Order", "name": "Name", "description": "Description", "prompts": "Prompts", "prompt": "prompt", "prompts_in_step": "Prompts in this step", "add_prompts": "Add Prompts", "no_prompts_in_step": "No prompts in this step yet", "click_add_prompts": "Click \"Add Prompts\" to get started", "add_prompts_to_step": "Add Prompts to Step", "add_prompts_description": "Select prompts to add to step {stepName}", "search_prompts": "Search prompts...", "select_all": "Select All", "deselect_all": "Deselect All", "selected": "selected", "loading_prompts": "Loading prompts...", "no_prompts_found": "No prompts found matching your search", "no_available_prompts": "No available prompts to add", "cancel": "Cancel", "add_selected_prompts": "Add {count} Prompts", "adding_prompts": "Adding...", "search_steps": "Search steps...", "error_loading_steps": "Error loading steps", "error_generic": "Something went wrong. Please try again.", "no_steps_found": "No steps found", "no_steps_description": "Steps will appear here once they are created.", "loading_more": "Loading more steps...", "load_more": "Load more steps", "parent_step_no_prompts": "Parent steps with child steps do not contain prompts.", "prompts_in_child_steps": "Prompts are managed in the child steps below."}, "workflow": {"common": {"uploadFileFail": "Failed to upload files", "uploadFileSuccess": "Files are uploaded successfully", "fileUploading": "File is uploading", "validateUrlFile": "Please enter a valid URL", "successProcessAdd": "URL added successfully", "uploadingFile": "Uploading files...", "clickUpload": "Click to upload", "textDescriptionDragAndDrop": "or drag and drop", "typeFile": "Images, PDFs, Word, Excel, PowerPoint files", "textAddURL": "Or upload from URL", "buttonUpload": "Upload", "titleFiles": " Uploaded Files / URL ", "inputPlaceholder": "Add file URL", "titleConfirmChange": "Confirm Changes", "titleUnSave": "UnSave Changes", "descriptionUnSave": "The changes made will be lost. Do you want to proceed?", "descriptionConfirm": "Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step", "descriptionGuard": "You have unsaved changes in the form. Are you sure you want to leave?", "descriptionDefault": "You have unsaved changes. Are you sure you want to leave? Your changes will be lost.", "messageLeave": "You have unsaved changes in the form. Are you sure you want to leave?", "analyzing": "Analyzing...", "processing": "Processing...", "loading": "Loading...", "nextStep": "Next Step", "previousStep": "Previous Step", "approve": "Approve", "cancel": "Cancel", "edit": "Edit", "continue": "Continue", "leaveAnyway": "Leave anyway", "stay": "Stay", "generate": "Generate", "discard": "Discard Change", "confirm": "Confirm", "back": "Back", "tryAgain": "Try Again", "other": "Other", "enter": "Enter", "backToDashboard": "Back to dashboard", "viewResearch": "View Research", "viewAnalysis": "View Analysis", "message": "*Please do not leave the page during AI generation process!", "taskCompleted": "Task completed successfully", "stepCompleted": "Step completed successfully", "errorValidStep": "Please complete the current step before proceeding", "taskNotFound": "Task not found", "backToUploadFile": "Back to upload file", "headerDefault": "Generated Research", "description": "This diagnosis provides a distilled understanding of the client’s situation and the potential of the project.", "completeAndNext": "Complete & Move to Next Task", "placeholderAnswer": "Enter your answer", "attachFile": "Attached Files", "saveNext": "Save & Next", "readyCopy": "Ready to Copy!", "descriptionCopy": "The analysis are ready to be copied to your clipboard.", "copied": "<PERSON>pied", "copyLink": "Copy link", "noticeInitialForm": "Initial screening form processed successfully", "updateConfirm": "Are you sure you want to update the status of this step? Changing the status will result in the deletion of all related information for this step.", "titleUpdateConfirm": "Are you sure you want to update?"}, "evaluationForm": {"messageSuccess": "Score calculation updated", "errorNextStep": "Please evaluate all criteria before submitting", "sectionATitle": "Section A - Client Profile", "sectionBTitle": "Section B - Financial Capacity & Collaboration History", "sectionCTitle": "Section C - Collaboration & Working Process", "sectionDTitle": "Section D - Growth Potential & Strategic Value", "evaluation": "Evaluation Criteria", "answer": "Answer", "confidence": "Confidence", "citation": "Citation", "type": "Criteria Type", "weight": "Weight", "score": "Criteria Score", "converted": "Converted Score", "totalScoring": "Total Scoring", "businessType": "Business type", "industryICPFit": "Industry & ICP fit", "revenueScale": "Revenue size", "annualMarketingBudget": "Annual marketing budget", "contractType": "Contract type", "paymentHistory": "Payment History", "workingHistoryWithMVVGroup": "History of Working with MVV Group", "projectFrequency": "Number of Project/Year", "accessToDecisionMakers": "Access to Decision Makers", "decisionMakingSpeed": "Decision Making Process", "complexityOfDecisionProcess": "Complexity of Decision Process", "marketingTeamStrength": "Marketing Team Strength", "willingnessToShareInformation": "Willingness to Share Information", "proActivenessInProjectCollaboration": "Pro-activeness in Project Collaboration", "culturalFit": "Cultural Fit", "longTermVisionCommitment": "Long-term Vision & Commitment", "potentialForMultiService": "Potential for Multi-Service", "crossCellAndUpCell": "Cross-sell/Up-sell Potential", "industryInfluence": "Industry Influence", "referralCapability": "Referral Capability", "sme": "SME (small, individual)", "spc": "Small private company", "domestic_corporation": "Domestic corporation", "mc": "Multinational corporation", "plc": "Publicly listed corporation", "oicp": "Outside ICP", "lr": "Loosely related", "wbds": "Within ICP but different segment", "rcs": "Right ICP, correct segment", "rsf": "Right ICP, strategic focus", "1_000_billion_vnd": "<1,000 billion VND", "1_000_2_000_billion_vnd": "1,000-2,000 billion VND", "2_000_4_000_billion_vnd": "2,000-4,000 billion VND", "4_000_6_000_billion_vnd": "4,000-6,000 billion VND", "6_000_billion_vnd": ">6,000 billion VND", "20_billion_vnd": "<20 billion VND", "20_50_billion_vnd": "20-50 billion VND", "50_100_billion_vnd": "50-100 billion VND", "100_200_billion_vnd": "100-200 billion VND", "200_billion_vnd": ">200 billion VND", "project_based_pitch": "Project-based pitch", "short_term_contract": "Short-term contract", "long_term_contract_6_12_months": "Long-term contract (6-12 months)", "agency_of_record_aor": "Agency of Record (AOR)", "long_term_exclusive": "Long-term exclusive", "often_30_days_late": "Often >30 days late", "15_30_days_late": "15-30 days late", "occasionally_late_pays_within_15_days": "Occasionally late, pays within 15 days", "always_on_time": "Always on time", "on_time_willing_to_pay_in_advance": "On time, willing to pay in advance", "conflicts_occurred": "Conflicts occurred", "poor_collaboration": "Poor collaboration", "average_collaboration_or_no_prior_cooperation": "Average collaboration or no prior cooperation", "good_collaboration": "Good collaboration", "excellent_collaboration_many_successful_projects": "Excellent collaboration, many successful projects", "1_project_year": "<1 project/year", "1_2_projects_year": "1-2 projects/year", "3_5_projects_year": "3-5 projects/year", "6_10_projects_year": "6-10 projects/year", "10_projects_year": ">10 projects/year", "no_access": "No access", "very_difficult_access": "Very difficult access", "occasionally_via_intermediaries": "Occasionally via intermediaries", "easily_accessible_when_needed": "Easily accessible when needed", "direct_and_frequent_access": "Direct and frequent access", "very_slow_2_months": "Very slow (>2 months)", "slow_1_2_months": "Slow (1-2 months)", "average_2_4_weeks": "Average (2-4 weeks)", "fast_1_2_weeks": "Fast (1-2 weeks)", "very_fast_1_week": "Very fast (<1 week)", "12_months_many_layers_of_approval": ">12 months, many layers of approval", "9_12_months": "9-12 months", "6_9_months": "6-9 months", "3_6_months": "3-6 months", "3_months_one_approval_level": "<3 months, one approval level", "no_team": "No team", "weak_team": "Weak team", "basic_team": "Basic team", "good_team_but_lacks_strategy": "Good team but lacks strategy", "strong_team_ready_to_execute_strategy": "Strong team, ready to execute strategy", "does_not_share": "Does not share", "limited_sharing": "Limited sharing", "basic_sharing_campaign_reports": "Basic sharing (campaign reports)", "good_sharing_dashboards_kpis": "Good sharing (dashboards, KPIs)", "excellent_sharing_real_time_tracking_crm": "Excellent sharing (real-time tracking, CRM)", "not_proactive_causes_delays": "Not proactive, causes delays", "slow_responses": "Slow responses", "on_time_delivery": "On-time delivery", "proactively_supportive": "Proactively supportive", "very_proactive_quick_responses_excellent_support": "Very proactive, quick responses, excellent support", "major_differences_prone_to_conflict": "Major differences, prone to conflict", "moderate_differences": "Moderate differences", "some_similarities": "Some similarities", "fairly_aligned_easy_to_collaborate": "Fairly aligned, easy to collaborate", "very_aligned_mutually_supportive": "Very aligned, mutually supportive", "no_commitment": "No commitment", "short_term_commitment_1_year": "Short-term commitment (<1 year)", "commitment_of_1_2_years": "Commitment of 1-2 years", "commitment_of_2_3_years": "Commitment of 2-3 years", "commitment_over_3_years": "Commitment over 3 years", "no_need": "No need", "small_need_1_service": "Small need (1 service)", "interest_in_2_services": "Interest in 2 services", "interest_in_multi_service_collaboration": "Interest in multi-service collaboration", "potential_to_use_entire_ecosystem_of_services": "Potential to use entire ecosystem of services", "no_potential": "No potential", "low_opportunity": "Low opportunity", "some_opportunities": "Some opportunities", "multiple_complementary_services": "Multiple complementary services", "high_growth_potential": "High growth potential", "no_influence": "No influence", "minor_influence": "Minor influence", "influence_in_a_niche": "Influence in a niche", "broad_industry_influence": "Broad industry influence", "iconic_customer_industry_leader": "Iconic customer, industry leader", "no_ability": "No ability", "low_referral_capability": "Low referral capability", "occasionally_refers": "Occasionally refers", "actively_refers": "Actively refers", "key_customer_creates_many_new_opportunities": "Key customer, creates many new opportunities", "extremelyImportant": "Extremely important", "important": "Important", "standard": "Standard", "sectionA": "Section A", "sectionB": "Section B", "sectionC": "Section C", "sectionD": "Section D", "scoreChart": "Score"}, "scoringOutcome": {"rankA": "Strategic clients – prioritized for long-term investment", "rankB": "Potential clients – possible to further develop", "rankC": "Non-priority clients – need further consideration", "score": "Score", "weight": "Weight", "weightScore": "Weight Score", "comment": "Comment", "rank": "Rank", "priorityScore": "Priority score", "conclusion": "Conclusion", "risk": "Potential Risks", "suggest": "Suggested tailored approaches", "criteria": "Criteria Proportion", "section": "Section Proportion"}, "clientUpload": {"campaignTitle": "Select campaign type", "fileTitle": "Attached Files"}, "businessForm": {"loadingMore": "Loading more research...", "noResearch": "No research analysis found. <PERSON><PERSON> 'Create new' to add one.", "noResearchFound": "No research found for", "description": "Most recent", "emptyMessage": "No items found.", "loadingMoreItem": "Loading more items...", "searching": "Searching...", "requiredFramework": "Research framework is required ", "requiredTemplate": "Research template is required ", "typeNote": "Specify your selection or upload new template below", "typePlaceholder": "Specify type of research", "descriptionNote": " Specify your description for new template below", "descriptionPlaceholder": "Specify description of research", "templateFile": "Template file upload", "dragAndDrop": "Drag & drop to upload", "files": " Files supported: PPT, PDF, DOC, MP3, MP4, TXT", "or": "or", "browser": "Browser files", "uploadFile": "Uploaded files", "clearButton": "Clear all files", "total": "Total", "tryAgain": "Try Again", "messageError": "An error occurred while loading items."}, "deskResearch": {"errorNextStep": "Attached file/URL is required.", "title": "Document Upload & Research Analysis", "description": "Upload related documents below to start generate the analysis", "editAnswer": "Edit Answer", "descriptionEditMode": "Human-in-the-loop: edit any AI-generated content to finalize answers.", "notificationSuccess": "Save research data successfully", "discoveryHeader": "Discovery Questionnaire", "descriptionHeader": "A set of key questions to understand the client’s context, goals, and challenges.", "readyTitle": "Ready to generate analysis?", "readyDescription": "The questionnaire will be closed once click Confirm.", "questionnaireSaved": "Discovery Questionnaire is saved successfully", "researchData": "Research Data", "summarizedReport": "Summarized Report"}, "questionnaire": {"header": "Optical Market Research Questionnaire", "time": "Time", "responses": "Number of responses", "status": "Status"}, "report": {"reportType": "Report", "presentType": "Presentation Deck", "header": "Comprehensive Report and Presentation Prep", "description": "Report and Presentation Outline Ready-to-use"}, "AI": {"name": "AI Assistant", "title": "Your Assistant", "placeholder": "Ask anything", "initial": "How can I help you ?", "instructions": "You are assisting the user as best as you can. Answer in the best way possible given the data you have."}, "research": {"deskResearch": "DeskResearch", "quantitative": "Quantitative Research", "qualitative": "Qualitative Research", "exploratory": "Exploratory Research", "descriptive": "Descriptive Research", "explanatory": "Explanatory Research", "filed": "Field Research", "experimental": "Experimental Research", "other": "Other Research", "report": "Report", "presentation": "Presentation Deck", "questionnaire": "Questionnaire", "analysis": "Analysis", "summary": "Summary", "createResearchSuccess": "Research created successfully", "createResearchFail": "Failed to create research", "updateResearchSuccess": "Research updated successfully", "updateResearchFail": "Failed to update research", "deleteResearchSuccess": "Research deleted successfully", "deleteResearchFail": "Failed to delete research", "createReportSuccess": "Report created successfully", "createReportFail": "Failed to create report", "updateReportSuccess": "Report updated successfully", "updateReportFail": "Failed to update report", "deleteReportSuccess": "Report deleted successfully", "deleteReportFail": "Failed to delete report"}}, "framework": {"common": {"edit": "Edit", "delete": "Delete", "cancel": "Cancel", "create": "Create", "update": "Update", "continue": "Continue"}, "header": "Framework & Templates Management", "description": "Create, edit, manage different framework and templates.", "title": "Framework & templates", "searchPlaceholder": "Search framework", "no": "No.", "researchType": "Research Type", "lastUpdated": "Last updated", "template": "Template", "questionnaire": "Questionnaire", "report": "Report", "deleteMessage": "Delete framework successfully", "messageModal": "This framework will be permanently deleted and cannot be undone once click Continue.", "create": "Create new", "messageError": "Framework name cannot be empty", "titleRequired": "Please enter all the required information to proceed.", "headerEdit": "Edit framework", "headerCreate": "Create framework", "fileUpload": "Template files upload", "unsaved": "Unsaved Changes", "messageUnsaved": "You have added new template data that hasn’t been saved. If you proceed, these changes will be lost.", "fileUploading": "File is uploading", "fileUploaded": "File is uploaded success", "messageTemplateName": "Template name cannot be empty", "emptyFile": "Please upload at least one file for the questionnaire or report.", "viewFile": "View file", "noFile": "No file", "browserFile": "Browser files", "add": "Add", "messageDefault": "All the documents within this company will be permanently deleted and cannot be undone once click Continue.", "messageTitle": "Are you sure to delete?", "name": "Framework", "messageTemplateNameExist": "Template name already exists. Please choose a different name."}, "paginator": {"previous": "Previous", "next": "Next"}, "main": {"project": "Project", "teamMembers": "Team Members", "framework": "Frameworks & Templates", "tryAgain": "Try Again", "noProjectFound": "No projects found.", "errorMessage": "An error occurred while loading projects.", "loadingMore": "Loading more projects...", "searchHeaderPlaceholder": "Search or type command...", "open": "Open", "viewInfo": "View Info", "markCompleted": "<PERSON> as Completed", "delete": "Delete", "menu": "<PERSON><PERSON>", "noticeHeader": "Project Complete", "descriptionHeader": "The project has been successfully completed", "complete": "Complete", "completing": "Completing...", "member": "member", "loading": "Loading...", "searching": "Searching...", "labelSelectOption": "Select option", "noResult": "No results found", "accountSetting": "Account settings", "signOut": "Sign out"}, "message": {"nameRequired": "Client name is required", "projectRequired": "Project name is required", "campaignRequired": "Project type is required", "endDateMustBeAfterStartDate": "End date must be after start date", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailValid": "Invalid email address", "passwordMin": "Password must be at least 8 characters", "passwordUpperCase": "Password must contain at least one uppercase letter", "passwordLowerCase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character", "roleRequired": "At least one role is required"}, "notice": {"teamMembersSuccess": "Team member created successfully", "teamMembersFail": "Failed to create team member", "teamMembersDeleteSuc": "Team member deleted successfully", "teamMembersDeleteFail": "Failed to delete team member", "projectCreateSuccess": "Project created successfully", "projectCreateFail": "Failed to create project", "projectUpdateSuccess": "Project updated successfully", "projectUpdateFail": "Failed to update project", "projectDeleteSuc": "Project deleted successfully", "projectDeleteFail": "Failed to delete project"}, "signIn": {"label": "Sign In", "description": "Enter your email and password to sign in!", "email": "Email", "password": "Password", "login": "Log In", "logging": "Logging in...", "placeholder": "Enter your password", "loginSuccess": "Login successful", "logoutSuccess": "Logged out successfully", "validEmail": "Invalid email address"}}